cmake_minimum_required(VERSION 3.20)
project(SmartSpectraAPIWrapper VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Find required packages - following SmartSpectra main project pattern
find_package(PkgConfig REQUIRED)

# PhysiologyEdge is the primary dependency - it provides protobuf, glog, absl, MediaPipe, etc.
find_package(PhysiologyEdge REQUIRED)
if(NOT TARGET Physiology::Edge)
    message(FATAL_ERROR "PhysiologyEdge package found but Physiology::Edge target not available. Please check PhysiologyEdge installation.")
endif()

# OpenGL for GPU support (following main project pattern)
find_package(OpenGL REQUIRED OpenGL GLES3)
if(NOT TARGET OpenGL::GL OR NOT TARGET OpenGL::GLES3)
    message(FATAL_ERROR "OpenGL or GLES3 not found. Please install OpenGL development packages.")
endif()

# Additional packages not provided by PhysiologyEdge
find_package(OpenCV REQUIRED)
if(OpenCV_VERSION VERSION_LESS "4.0")
    message(FATAL_ERROR "OpenCV version ${OpenCV_VERSION} found, but version 4.0 or higher is required.")
endif()

find_package(Threads REQUIRED)
if(NOT TARGET Threads::Threads)
    message(FATAL_ERROR "Threading support not available.")
endif()

# Include SmartSpectra SDK with proper error checking
set(SMARTSPECTRA_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../cpp")
set(SMARTSPECTRA_INCLUDE_DIR "${SMARTSPECTRA_ROOT}")
set(SMARTSPECTRA_LIB_DIR "${SMARTSPECTRA_ROOT}/build")

# Verify SmartSpectra SDK is available
if(NOT EXISTS "${SMARTSPECTRA_ROOT}")
    message(FATAL_ERROR "SmartSpectra SDK not found at ${SMARTSPECTRA_ROOT}. Please ensure the main C++ project is available.")
endif()

if(NOT EXISTS "${SMARTSPECTRA_LIB_DIR}")
    message(FATAL_ERROR "SmartSpectra build directory not found at ${SMARTSPECTRA_LIB_DIR}. Please build the main C++ project first.")
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${SMARTSPECTRA_INCLUDE_DIR}
    ${OpenCV_INCLUDE_DIRS}
)

# Add Crow C++ framework using FetchContent
include(FetchContent)
FetchContent_Declare(
    crow
    GIT_REPOSITORY https://github.com/CrowCpp/Crow.git
    GIT_TAG v1.2.1
)
FetchContent_MakeAvailable(crow)

# Add nlohmann/json for JSON handling
FetchContent_Declare(
    json
    GIT_REPOSITORY https://github.com/nlohmann/json.git
    GIT_TAG v3.11.3
)
FetchContent_MakeAvailable(json)

# Add UUID library for session ID generation
FetchContent_Declare(
    stduuid
    GIT_REPOSITORY https://github.com/mariusbancila/stduuid.git
    GIT_TAG v1.2.3
)
FetchContent_MakeAvailable(stduuid)

# Source files
set(API_SOURCES
    src/main.cpp
    src/FrameBufferVideoSource.cpp
)

# Create the executable
add_executable(smartspectra_api_server ${API_SOURCES})

# Auto-detect SmartSpectra libraries
set(SMARTSPECTRA_REQUIRED_LIBS
    "smartspectra/container/libSmartSpectraContainer.a"
    "smartspectra/video_source/libSmartSpectraVideoSource.a"
    "smartspectra/video_source/libSmartSpectraVideoInterface.a"
    "smartspectra/video_source/file_stream/libSmartSpectraVideoSource_FileStream.a"
    "smartspectra/video_source/camera/libSmartSpectraVideoSource_Camera.a"
    "smartspectra/gui/libSmartSpectraGui.a"
)

set(SMARTSPECTRA_LIBS "")
foreach(lib ${SMARTSPECTRA_REQUIRED_LIBS})
    set(lib_path "${SMARTSPECTRA_LIB_DIR}/${lib}")
    if(EXISTS "${lib_path}")
        list(APPEND SMARTSPECTRA_LIBS "${lib_path}")
        message(STATUS "Found SmartSpectra library: ${lib}")
    else()
        message(FATAL_ERROR "Required SmartSpectra library not found: ${lib_path}")
    endif()
endforeach()

# Link SmartSpectra SDK libraries first
target_link_libraries(smartspectra_api_server
    PRIVATE
    ${SMARTSPECTRA_LIBS}
)

# Link other libraries - PhysiologyEdge provides protobuf, glog, absl, etc.
target_link_libraries(smartspectra_api_server
    PRIVATE
    Physiology::Edge  # Primary dependency - provides protobuf, glog, absl, MediaPipe
    Crow::Crow
    nlohmann_json::nlohmann_json
    stduuid
    ${OpenCV_LIBS}
    Threads::Threads
    OpenGL::GL
    OpenGL::GLES3
)

# Compiler definitions
target_compile_definitions(smartspectra_api_server
    PRIVATE
    CROW_ENABLE_SSL=0  # Disable SSL for simplicity, can be enabled later
    CROW_ENABLE_COMPRESSION=1
)

# Include SmartSpectra headers
target_include_directories(smartspectra_api_server
    PRIVATE
    ${SMARTSPECTRA_INCLUDE_DIR}
)

# Installation
install(TARGETS smartspectra_api_server
    RUNTIME DESTINATION bin
)

# Copy sample files
install(DIRECTORY samples/
    DESTINATION share/smartspectra-api/samples
    FILES_MATCHING PATTERN "*.html" PATTERN "*.js"
)

# Copy documentation
install(FILES docs/openapi.yaml
    DESTINATION share/smartspectra-api/docs
    OPTIONAL
)

# Environment variable checks
if(DEFINED ENV{SMARTSPECTRA_API_KEY})
    message(STATUS "SMARTSPECTRA_API_KEY environment variable is set")
else()
    message(WARNING "SMARTSPECTRA_API_KEY environment variable is not set. The API server will fail to start without it.")
endif()

# Print configuration summary
message(STATUS "SmartSpectra API Wrapper Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OpenCV version: ${OpenCV_VERSION}")
message(STATUS "  SmartSpectra root: ${SMARTSPECTRA_ROOT}")
message(STATUS "  SmartSpectra libraries found: ${SMARTSPECTRA_LIBS}")
message(STATUS "  PhysiologyEdge target: Available")
message(STATUS "  OpenGL/GLES3 support: Available")
