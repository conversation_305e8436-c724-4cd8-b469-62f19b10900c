version: '3.8'

services:
  smartspectra-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SMARTSPECTRA_API_KEY=${SMARTSPECTRA_API_KEY}
      - API_SERVER_PORT=8080
      - API_SERVER_HOST=0.0.0.0
      - API_MAX_SESSIONS=100
    volumes:
      # Mount samples directory for easy access
      - ./samples:/app/samples:ro
      # Mount logs directory (optional)
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./samples:/usr/share/nginx/html/samples:ro
    depends_on:
      - smartspectra-api
    restart: unless-stopped
    profiles:
      - production

volumes:
  logs:
    driver: local
