<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartSpectra Web Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        #webcam {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #000;
        }
        
        .controls {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        fieldset {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        legend {
            font-weight: bold;
            color: #555;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        #startButton {
            background-color: #4CAF50;
            color: white;
        }
        
        #startButton:hover:not(:disabled) {
            background-color: #45a049;
        }
        
        #stopButton {
            background-color: #f44336;
            color: white;
        }
        
        #stopButton:hover:not(:disabled) {
            background-color: #da190b;
        }
        
        .metrics-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .metric-unit {
            font-size: 14px;
            color: #888;
        }
        
        #blink-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ccc;
            margin: 10px auto;
            transition: background-color 0.1s ease;
        }
        
        .blink {
            background-color: #ffeb3b !important;
            box-shadow: 0 0 20px #ffeb3b;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .status.disconnected {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .status.connecting {
            background-color: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ff9800;
        }
        
        .status.connected {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.error {
            color: #d32f2f;
        }
        
        .log-entry.info {
            color: #1976d2;
        }
        
        .log-entry.warning {
            color: #f57c00;
        }
        
        @media (max-width: 768px) {
            .video-section {
                flex-direction: column;
            }
            
            #webcam {
                width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SmartSpectra Web Client</h1>
        
        <div class="video-section">
            <video id="webcam" width="640" height="480" autoplay muted playsinline></video>
            <canvas id="canvas" style="display:none;"></canvas>
            
            <div class="controls">
                <div id="status" class="status disconnected">Disconnected</div>
                
                <fieldset>
                    <legend>Analysis Mode</legend>
                    <input type="radio" id="modeContinuous" name="analysisMode" value="continuous" checked>
                    <label for="modeContinuous">Continuous</label>
                    <input type="radio" id="modeSpot" name="analysisMode" value="spot">
                    <label for="modeSpot">Spot (30 seconds)</label>
                </fieldset>
                
                <fieldset>
                    <legend>Video Quality</legend>
                    <select id="resolution">
                        <option value="480p">480p (640x480)</option>
                        <option value="720p" selected>720p (1280x720)</option>
                        <option value="1080p">1080p (1920x1080)</option>
                    </select>
                </fieldset>
                
                <div class="button-group">
                    <button id="startButton">Start Analysis</button>
                    <button id="stopButton" disabled>Stop Analysis</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h2>Live Physiological Metrics</h2>
        <div class="metrics-section">
            <div class="metric-card">
                <div class="metric-label">Heart Rate</div>
                <div class="metric-value" id="pulse">--</div>
                <div class="metric-unit">BPM</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-label">Respiratory Rate</div>
                <div class="metric-value" id="breaths">--</div>
                <div class="metric-unit">RPM</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-label">Blink Detection</div>
                <div id="blink-indicator"></div>
                <div class="metric-unit">Real-time</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h3>Activity Log</h3>
        <div id="log" class="log"></div>
    </div>

    <script src="app.js"></script>
</body>
</html>
