#!/bin/bash
set -e

# Update package lists
sudo apt update

# Install build tools
sudo apt install -y build-essential cmake ninja-build

# Setup Presage Technologies repository
curl -s "https://presage-security.github.io/PPA/KEY.gpg" | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/presage-technologies.gpg >/dev/null
sudo curl -s --compressed -o /etc/apt/sources.list.d/presage-technologies.list "https://presage-security.github.io/PPA/presage-technologies.list"
sudo apt update

# Print success message
echo "========================================================"
echo "Setup complete! Now you can install SmartSpectra dependencies with:"
echo "sudo apt install libphysiologyedge-dev"
echo "========================================================"
