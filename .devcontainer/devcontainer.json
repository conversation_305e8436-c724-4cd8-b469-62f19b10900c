{"name": "SmartSpectra Ubuntu 22.04", "image": "mcr.microsoft.com/devcontainers/base:ubuntu-22.04", "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": "true", "username": "vscode", "userUid": "1000", "userGid": "1000", "upgradePackages": "true"}, "ghcr.io/devcontainers/features/git:1": {"version": "latest", "ppa": "false"}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["ms-vscode.cpptools", "ms-vscode.cmake-tools"], "settings": {"terminal.integrated.defaultProfile.linux": "bash"}}}, "postCreateCommand": "sudo apt update && sudo apt install -y build-essential cmake ninja-build"}